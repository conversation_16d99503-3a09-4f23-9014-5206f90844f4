
import React, { useEffect, useRef } from 'react';
import { X, Loader } from 'lucide-react';

interface PaInfoModalProps {
  paId: string;
  paFullName: string;
  content: string | null;
  isLoading: boolean;
  onClose: () => void;
}

const PaInfoModal: React.FC<PaInfoModalProps> = ({ paId, paFullName, content, isLoading, onClose }) => {
  const modalRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        onClose();
      }
    };

    const handleClickOutside = (event: MouseEvent) => {
      if (modalRef.current && !modalRef.current.contains(event.target as Node)) {
        onClose();
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    document.addEventListener('mousedown', handleClickOutside);

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [onClose]);

  return (
    <div 
      className="fixed inset-0 z-50 flex items-center justify-center bg-gray-950/80 backdrop-blur-sm"
      aria-labelledby="pa-modal-title"
      role="dialog"
      aria-modal="true"
    >
      <div 
        ref={modalRef}
        className="bg-gray-900 border border-gray-700 rounded-xl shadow-2xl w-full max-w-2xl max-h-[80vh] flex flex-col"
      >
        <header className="flex items-center justify-between p-4 border-b border-gray-700 flex-shrink-0">
          <h2 id="pa-modal-title" className="text-xl font-bold text-brand-primary">
            CMMI PA: <span className="text-white">{paFullName} ({paId})</span>
          </h2>
          <button 
            onClick={onClose} 
            className="text-gray-400 hover:text-white transition-colors rounded-full p-1 focus:outline-none focus:ring-2 focus:ring-brand-primary"
            aria-label="Close modal"
          >
            <X size={24} />
          </button>
        </header>

        <main className="p-6 overflow-y-auto text-gray-300">
          {isLoading ? (
            <div className="flex items-center justify-center h-48">
              <Loader size={32} className="animate-spin text-brand-primary" />
            </div>
          ) : (
            <div className="prose prose-invert prose-sm md:prose-base max-w-none whitespace-pre-wrap">
              {content}
            </div>
          )}
        </main>
        
        <footer className="p-3 bg-gray-950/50 border-t border-gray-800 rounded-b-xl flex-shrink-0">
            <p className="text-xs text-center text-gray-500">Content generated by Ignition AI</p>
        </footer>
      </div>
    </div>
  );
};

export default PaInfoModal;
