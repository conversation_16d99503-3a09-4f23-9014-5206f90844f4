{"name": "ignition-ai-project-dashboard", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "echo '<PERSON><PERSON> not configured yet'", "test": "vitest", "test:ui": "vitest --ui", "test:run": "vitest run", "test:coverage": "vitest run --coverage", "test:watch": "vitest --watch", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "test:e2e:headed": "playwright test --headed", "test:e2e:debug": "playwright test --debug", "test:all": "npm run test:run && npm run test:e2e", "test:ci": "npm run test:coverage && npm run test:e2e", "playwright:install": "playwright install", "docker:build": "docker build -t ignition:latest .", "docker:build-dev": "docker build -f Dockerfile.dev -t ignition:dev .", "docker:run": "docker run -p 8080:80 ignition:latest", "docker:run-dev": "docker run -p 3000:3000 -v $(pwd):/app ignition:dev", "k8s:deploy": "kubectl apply -f k8s/", "k8s:delete": "kubectl delete -f k8s/", "electron": "concurrently \"npm run dev\" \"wait-on http://localhost:5174 && electron .\"", "electron:dev": "concurrently \"npm run dev\" \"wait-on http://localhost:5174 && electron . --dev\"", "electron:build": "npm run build && electron-builder", "electron:build:win": "npm run build && electron-builder --win", "electron:build:mac": "npm run build && electron-builder --mac", "electron:build:linux": "npm run build && electron-builder --linux", "electron:dist": "npm run build && electron-builder --publish=never"}, "dependencies": {"@google/genai": "^1.8.0", "@hello-pangea/dnd": "^18.0.1", "@types/d3": "^7.4.3", "chart.js": "^4.5.0", "d3": "^7.9.0", "framer-motion": "^12.23.0", "lucide-react": "^0.525.0", "react": "^19.1.0", "react-chartjs-2": "^5.3.0", "react-dom": "^19.1.0", "react-hot-toast": "^2.5.2", "recharts": "^3.0.2", "uuid": "^9.0.1"}, "devDependencies": {"@playwright/test": "^1.53.2", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/jest": "^30.0.0", "@types/node": "^22.14.0", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@types/testing-library__jest-dom": "^6.0.0", "@vitest/coverage-v8": "^3.2.4", "@vitest/ui": "^3.2.4", "concurrently": "^9.2.0", "electron": "^37.2.0", "electron-builder": "^26.0.12", "jest": "^30.0.4", "jest-environment-jsdom": "^30.0.4", "jsdom": "^26.1.0", "typescript": "~5.7.2", "vite": "^6.2.0", "vitest": "^3.2.4", "wait-on": "^8.0.3"}, "main": "electron/main.cjs", "homepage": "./", "build": {"appId": "com.castlebravo.ignition", "productName": "Ignition AI Dashboard", "directories": {"output": "dist-electron"}, "files": ["dist/**/*", "electron/**/*", "node_modules/**/*"], "mac": {"category": "public.app-category.productivity", "target": [{"target": "dmg", "arch": ["x64", "arm64"]}]}, "win": {"target": [{"target": "nsis", "arch": ["x64"]}], "forceCodeSigning": false}, "linux": {"target": [{"target": "AppImage", "arch": ["x64"]}]}, "publish": {"provider": "github", "owner": "castle-bravo-project", "repo": "ignition"}}}