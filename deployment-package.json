{"name": "ignition-github-app", "private": true, "version": "1.0.0", "type": "module", "description": "AI-powered compliance and project management platform with meta-compliance capabilities", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "predeploy": "npm run build", "deploy": "gh-pages -d dist"}, "dependencies": {"@google/genai": "^1.8.0", "@hello-pangea/dnd": "^18.0.1", "chart.js": "^4.5.0", "d3": "^7.9.0", "framer-motion": "^12.23.0", "lucide-react": "^0.525.0", "react": "^19.1.0", "react-chartjs-2": "^5.3.0", "react-dom": "^19.1.0", "react-hot-toast": "^2.5.2", "recharts": "^3.0.2", "uuid": "^9.0.1"}, "devDependencies": {"@types/d3": "^7.4.3", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@types/uuid": "^9.0.8", "@vitejs/plugin-react": "^4.3.4", "gh-pages": "^6.1.1", "typescript": "~5.7.2", "vite": "^6.2.0"}, "keywords": ["compliance", "project-management", "ai", "github-app", "meta-compliance", "audit", "quality-assurance"], "repository": {"type": "git", "url": "https://github.com/castle-bravo-project/ignition-github-app.git"}}