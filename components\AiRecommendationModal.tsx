import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON> } from 'lucide-react';
import React, { useEffect, useRef } from 'react';

interface AiRecommendationModalProps {
  paName: string;
  content: string | null;
  isLoading: boolean;
  onClose: () => void;
}

const AiRecommendationModal: React.FC<AiRecommendationModalProps> = ({ paName, content, isLoading, onClose }) => {
  const modalRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => event.key === 'Escape' && onClose();
    const handleClickOutside = (event: MouseEvent) => {
      if (modalRef.current && !modalRef.current.contains(event.target as Node)) {
        onClose();
      }
    };
    document.addEventListener('keydown', handleKeyDown);
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [onClose]);

  // Basic markdown to HTML conversion for lists
  const formatContent = (text: string | null) => {
    if (!text) return null;
    
    const elements = text
      .split('\n')
      .map((line, index) => {
        if (line.startsWith('- ') || line.startsWith('* ')) {
          return <li key={index} className="mb-2">{line.substring(2)}</li>;
        }
        if (line.trim() === '') {
            return <br key={index} />;
        }
        return <p key={index}>{line}</p>;
      });

    return elements.reduce((acc: React.ReactElement[], elem) => {
      const lastElement = acc.length > 0 ? acc[acc.length - 1] : null;

      if (elem.type === 'li') {
        if (lastElement && lastElement.type === 'ul') {
          // Add to existing ul
          const newChildren = [...React.Children.toArray((lastElement as any).props.children), elem];
          acc[acc.length - 1] = React.cloneElement(lastElement as any, { children: newChildren });
        } else {
          // Create new ul
          acc.push(<ul key={`ul-${acc.length}`} className="list-disc list-outside pl-5 space-y-2">{elem}</ul>);
        }
      } else {
        // Not a list item
        acc.push(elem);
      }
      return acc;
    }, []);
  };

  return (
    <div 
      className="fixed inset-0 z-50 flex items-center justify-center bg-gray-950/80 backdrop-blur-sm"
      aria-labelledby="ai-modal-title"
      role="dialog"
      aria-modal="true"
    >
      <div 
        ref={modalRef}
        className="bg-gray-900 border border-gray-700 rounded-xl shadow-2xl w-full max-w-2xl max-h-[80vh] flex flex-col"
      >
        <header className="flex items-center justify-between p-4 border-b border-gray-700 flex-shrink-0">
          <h2 id="ai-modal-title" className="text-xl font-bold text-brand-primary flex items-center gap-3">
            <Bot size={24} />
            <span>AI Recommendations for <span className="text-white">{paName}</span></span>
          </h2>
          <button 
            onClick={onClose} 
            className="text-gray-400 hover:text-white transition-colors rounded-full p-1 focus:outline-none focus:ring-2 focus:ring-brand-primary"
            aria-label="Close modal"
          >
            <X size={24} />
          </button>
        </header>

        <main className="p-6 overflow-y-auto text-gray-300">
          {isLoading ? (
            <div className="flex flex-col items-center justify-center h-48 text-center">
              <Loader size={32} className="animate-spin text-brand-primary mb-4" />
              <p className="text-lg">Ignition AI is analyzing your project...</p>
              <p className="text-sm text-gray-400">This may take a moment.</p>
            </div>
          ) : (
            <div className="prose prose-invert prose-sm md:prose-base max-w-none">
              {formatContent(content)}
            </div>
          )}
        </main>
        
        <footer className="px-4 py-2 bg-gray-950/50 border-t border-gray-800 rounded-b-xl flex-shrink-0">
            <p className="text-xs text-center text-gray-500">Content generated by Ignition AI. Always review for accuracy.</p>
        </footer>
      </div>
    </div>
  );
};

export default AiRecommendationModal;