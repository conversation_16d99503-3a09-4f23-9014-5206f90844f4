# Ignition AI Project Dashboard - Environment Configuration Template
# Copy this file to .env.local and fill in your actual values

# =============================================================================
# REQUIRED: Gemini AI API Configuration
# =============================================================================
# Get your API key from: https://aistudio.google.com/
# Both variables are needed for compatibility with different parts of the app
GEMINI_API_KEY=your_gemini_api_key_here
VITE_GEMINI_API_KEY=your_gemini_api_key_here

# =============================================================================
# OPTIONAL: GitHub Integration
# =============================================================================
# GitHub Personal Access Token for repository integration
# Required scopes: repo (for private repos) or public_repo (for public repos)
# Get your PAT from: https://github.com/settings/tokens
VITE_GITHUB_PAT=your_github_personal_access_token_here

# Default GitHub repository URL for project data storage
VITE_GITHUB_REPO_URL=https://github.com/your-org/your-repo

# Default file path within the repository for storing project data
VITE_GITHUB_FILE_PATH=ignition-project.json

# =============================================================================
# OPTIONAL: API Configuration
# =============================================================================
# API base URL (if using a custom backend)
VITE_API_URL=http://localhost:3000

# API request timeout in milliseconds
VITE_API_TIMEOUT=30000

# =============================================================================
# OPTIONAL: Feature Flags
# =============================================================================
# Enable/disable AI-powered features
VITE_ENABLE_AI_FEATURES=true

# Enable/disable GitHub integration features
VITE_ENABLE_GITHUB_INTEGRATION=true

# Enable/disable quality gate validation
VITE_ENABLE_QUALITY_GATES=true

# Enable/disable compliance checking features
VITE_ENABLE_COMPLIANCE_CHECKS=true

# Enable/disable process asset framework
VITE_ENABLE_PROCESS_ASSETS=true

# =============================================================================
# OPTIONAL: Development Configuration
# =============================================================================
# Development mode settings
NODE_ENV=development

# Enable development debugging
VITE_DEBUG=false

# Enable verbose logging
VITE_VERBOSE_LOGGING=false

# =============================================================================
# OPTIONAL: Production Configuration
# =============================================================================
# Production optimizations
VITE_BUILD_SOURCEMAP=false

# Analytics and monitoring (if implemented)
VITE_ANALYTICS_ID=your_analytics_id_here

# Error reporting service (if implemented)
VITE_SENTRY_DSN=your_sentry_dsn_here

# =============================================================================
# SETUP INSTRUCTIONS
# =============================================================================
# 1. Copy this file: cp .env.example .env.local
# 2. Edit .env.local with your actual values
# 3. At minimum, set GEMINI_API_KEY and VITE_GEMINI_API_KEY
# 4. Restart the development server: npm run dev
# 5. Test AI features in the application
#
# For production deployment:
# - Use environment-specific files (.env.production, .env.staging)
# - Never commit actual API keys to version control
# - Use secure secret management in production environments
