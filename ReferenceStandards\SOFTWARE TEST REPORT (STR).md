## DATA ITEM DESCRIPTION

Title: SOFTWARE TEST REPORT (STR)

```
Number: DI-IPSC-81440A Approval Date: 19991215
```
# AMSC Number: N7365 Limitation:

```
DTIC Applicable: GIDEP Applicable:
Office of Primary Responsibility: N/SPAWAR
Applicable Forms:
Use, Relationships:
```
```
The Software Test Report (STR) is a record of the qualification testing performed on a Computer
Software Configuration Item (CSCI), a software system or subsystem, or other software-related
```
# item.

# The STR enables the acquirer to assess the testing and its results.

```
This Data Item Description (DID) contains the format and content preparation instructions for
the data product generated by specific and discrete task requirements as delineated in the
```
# contract.

# This DID is used when the developer is tasked to analyze and record the results of CSCI

```
qualification testing, system qualification testing of a software system, or other testing identified
in the contract.
```
# This DID supersedes DI-IPSC-81440.

```
Requirements:
```
1. Reference documents. None.

# 2. General instructions.

```
a. Automated techniques. Use of automated techniques is encouraged. The term
“document” in this DID means a collection of data regardless of its medium.
```
```
b. Alternate presentation styles. Diagrams, tables, matrices, and other presentation styles
are acceptable substitutes for text when data required by this DID can be made more readable
using these styles.
```
3. Format. Following are the format requirements.

# The report shall be in contractor format unless otherwise specified on the Contract Data

```
Requirements List (CDRL)(DD 1423). The CDRL should specify whether deliverable data are
to be delivered on paper or electronic media; are to be in a given electronic form (such as ASCIL,
CALS, or compatible with a specified word processor or other support software); may be
```
```
Source: http://assist.dla.mil -- Downloaded: 2025-07-07T22:23Z
```

```
delivered in developer format rather than in the format specified herein; and may reside in a
computer-aided software engineering (CASE) or other automated tool rather than in the form of
a traditional document.
```
4. Content. The report shall contain the following:

```
a. Title page or identifier. The document shall include a title page containing, as
applicable: document number; volume number; version/revision indicator; security markings or
other restrictions on the handling of the document; date; document title; name, abbreviation, and
any other identifier for the system, subsystem, or item to which the document applies; contract
number; CDRL item number; organization for which the document has been prepared; name and
address of the preparing organization; and distribution statement. For data in a database or other
alternative form, this information shall be included on external and internal labels or by
equivalent identification methods.
```
```
b. Table of contents. The document shall contain a table of contents providing the
number, title, and page number of each titled paragraph, figure, table, and appendix. For data in
a database or other alternative form, this information shall consist of an internal or external table
of contents containing pointers to, or instructions for accessing, each paragraph, figure, table, and
appendix or their equivalents.
```
```
c. Page numbering/labeling. Each page shall contain a unique page number and display
the document number, including version, volume, and date, as applicable. For data in a database
or other alternative form, files, screens, or other entities shall be assigned names or numbers in
such a way that desired data can be indexed and accessed.
```
```
d. Response to tailoring instructions. If a paragraph is tailored out of this DID, the
resulting document shall contain the corresponding paragraph number and title, followed by
“This paragraph has been tailored out.” For data in a database or other alternative form, this
representation need occur only in the table of contents or equivalent.
```
```
e. Multiple paragraphs and subparagraphs. Any section, paragraph, or subparagraph in
this DID may be written as multiple paragraphs or subparagraphs to enhance readability.
```
```
f. Standard data descriptions. If a data description required by this DID has been
published in a standard data element dictionary specified in the contract, reference to an entry in
that dictionary is preferred over including the description itself.
```
```
g. Substitution of existing documents. Commercial or other existing documents may be
substituted for all or part of the document if they contain the required data.
```
```
The numbers shown designate the paragraph numbers to be used in the document.
```
1. Scope. This section shall be divided into the following paragraphs.

```
Source: http://assist.dla.mil -- Downloaded: 2025-07-07T22:23Z
```

1.1 Identification. This paragraph shall contain a full identification of the system and the
software to which this document applies, including, as applicable, identification number(s),
title(s), abbreviation(s), version number(s), and release number(s).

```
1.2 System overview. This paragraph shall briefly state the purpose of the system and the
```
# software to which this document applies. It shall describe the general nature of the system and

software; summarize the history of system development, operation, and maintenance; identify
the project sponsor, acquirer, user, developer, and support agencies; identify current and planned
operating sites; and list other relevant documents.

1.3 Document overview. This paragraph shall summarize the purpose and contents of
this document and shall describe any security or privacy considerations associated with its use.

2. Referenced documents. This section shall list the number, title, revision, and date of
all documents referenced in this document. This section shall also identify the source for all
documents not available through normal Government stocking activities.

# 3. Overview of test results. This section shall be divided into the following paragraphs

to provide an overview of test results.

```
3.1 Overall assessment of the software tested. This paragraph shall:
```
a. Provide an overall assessment of the software as demonstrated by the test results in
this report

b. Identify any remaining deficiencies, limitations, or constraints that were detected by
the testing performed. Problem/change reports may be used to provide deficiency information.

```
c. For each remaining deficiency, limitation, or constraint, describe:
```
1) Its impact on software and system performance, including identification of
requirements not met

```
2) The impact on software and system design to correct it
```
```
3) A recommended solution/approach for correcting it
```
3.2 Impact of test environment. This paragraph shall provide an assessment of the
manner in which the test environment may be different from the operational environment and the
effect of this difference on the test results.

3.3 Recommended improvements. This paragraph shall provide any recommended
improvements in the design, operation, or testing of the software tested. A discussion of each
recommendation and its impact on the software may be provided. If no recommended
improvements are provided, this paragraph shall state “None.”

```
Source: http://assist.dla.mil -- Downloaded: 2025-07-07T22:23Z
```

4. Detailed test results. This section shall be divided into the following paragraphs to

# describe the detailed results for each test. Note: The word “test” means a related collection of

test cases.

4.x (Project-unique identifier of a test). This paragraph shall identify a test by project-
unique identifier and shall be divided into the following subparagraphs to describe the test

# results.

```
4.x.1 Summary of test results. This paragraph shall summarize the results of the test.
```
# The summary shall include, possibly in a table, the completion status of each test case associated

# with the test (for example, “all results as expected,” “problems encountered,” “deviations

required”). When the completion status is not “as expected,” this paragraph shall reference the
following paragraphs for details.

```
4.x.2 Problems encountered. This paragraph shall be divided into subparagraphs that
identify each test case in which one or more problems occurred.
```
4x.2.y (Project-unique identifier of a test case). This paragraph shall identify by project-
unique identifier a test case in which one or more problems occurred, and shall provide:

```
a. A brief description of the problem(s) that occurred
```
```
b. Identification of the test procedure step(s) in which they occurred
```
```
c. Reference(s) to the associated problem/change report(s) and backup data, as applicable
```
```
d. The number of times the procedure or step was repeated in attempting to correct the
problem(s) and the outcome of each attempt
```
```
e. Back-up points or test steps where tests were resume for retesting
```
```
4.x.3 Deviations from test cases/procedures. This paragraph shall be divided into
subparagraphs that identify each test case in which deviations from test case/test procedures
occurred.
```
```
4.x.3.y (Project-unique identifier of a test case). This paragraph shall identify by project-
unique identifier a test case in which one or more deviations occurred, and shall provide:
```
```
a. A description of the deviation(s) (for example, test case run in which the deviation
occurred and nature of the deviation, such as substitution of required equipment, procedural steps
not followed, schedule deviations). (Red-lined test procedures may be used to show the
deviations)
```
```
b. The rationale for the deviation(s)
```
# c. An assessment of the deviations’ impact on the validity of the test case

```
Source: http://assist.dla.mil -- Downloaded: 2025-07-07T22:23Z
```

5. Testlog. This section shall present, possibly in a figure or appendix, a chronological
record of the test events covered by this report. This test log shall include:

```
a. The date(s), time(s), and location(s) of the tests performed
```
b. The hardware and software configurations used for each test including, as applicable,
part/model/serial number, manufacturer, revision level, and calibration date of all hardware, and
version number and name for the software components used

c. The date and time of each test-related activity, the identify of the individual(s) who
performed the activity, and the identities of witnesses, as applicable

# 6. Notes. This section shall contain any general information that aids in understanding

this document (e.g., background information, glossary, rationale). This section shall include an
alphabetical listing of all acronyms, abbreviations, and their meanings as used in this document
and a list of any terms and definitions needed to understand this document.

A. Appendices. Appendices may be used to provide information published separately
for convenience in document maintenance (e.g., charts classified data). As applicable, each
appendix shall be referenced in the main body of the document where the data would normally
have been provided. Appendixes may be bound as separate documents for ease in handling.

# Appendixes shall be lettered alphabetically (A, B, etc.).

## END OF DI-IPSC-81440A

```
Source: http://assist.dla.mil -- Downloaded: 2025-07-07T22:23Z
```

