import React, { useState } from 'react';
import { PrAnalysisResult, PullRequest, Requirement, ConfigurationItem, Risk } from '../types';
import { <PERSON><PERSON>, Loader, AlertTriangle, CheckSquare, Package, ShieldAlert, GitCommitVertical, Copy, Check, MessageSquare } from 'lucide-react';

interface PrAnalysisViewProps {
  pr: PullRequest;
  result?: PrAnalysisResult & { error?: string };
  isLoading: boolean;
  onPostComment: (prNumber: number, commentBody: string) => Promise<void>;
}

const LinkedItemTag: React.FC<{ item: Requirement | ConfigurationItem | Risk }> = ({ item }) => {
    let icon;
    let colorClasses;

    if ('priority' in item) { // Requirement
        icon = <CheckSquare size={14} />;
        colorClasses = 'bg-blue-900/50 text-blue-300 border-blue-700';
    } else if ('type' in item) { // ConfigurationItem
        icon = <Package size={14} />;
        colorClasses = 'bg-purple-900/50 text-purple-300 border-purple-700';
    } else { // Risk
        icon = <ShieldAlert size={14} />;
        colorClasses = 'bg-red-900/50 text-red-300 border-red-700';
    }

    return (
        <div className={`inline-flex items-center gap-2 px-2.5 py-1 text-sm rounded-md border ${colorClasses}`}>
            {icon}
            <span className="font-mono text-xs font-bold">{item.id}</span>
            <span className="truncate">{('description' in item) ? item.description : item.name}</span>
        </div>
    )
}

const PrAnalysisView: React.FC<PrAnalysisViewProps> = ({ pr, result, isLoading, onPostComment }) => {
    const [isCopied, setIsCopied] = useState(false);
    const [isPosting, setIsPosting] = useState(false);

    const handleCopy = () => {
        if (!result || !result.suggestedCommitMessage) return;
        navigator.clipboard.writeText(result.suggestedCommitMessage).then(() => {
            setIsCopied(true);
            setTimeout(() => setIsCopied(false), 2000);
        });
    };

    const generateCommentBody = (): string => {
        if (!result) return "";
        let body = `### <img src="https://raw.githubusercontent.com/castle-bravo/ignition/main/public/favicon.svg" width="24" height="24" alt="Ignition Logo" /> Ignition AI Analysis for PR #${pr.number}\n\n`;
        body += `**Summary:**\n${result.summary}\n\n---\n\n`;

        if (result.linkedRequirements.length > 0) {
            body += `**<g-emoji class="g-emoji" alias="ballot_box_with_check" fallback-src="https://github.githubassets.com/images/icons/emoji/unicode/2611.png">✅</g-emoji> Linked Requirements:**\n`;
            result.linkedRequirements.forEach(r => {
                body += `- \`${r.id}\`: ${r.description}\n`;
            });
            body += "\n";
        }
        
        if (result.linkedCis.length > 0) {
            body += `**<g-emoji class="g-emoji" alias="package" fallback-src="https://github.githubassets.com/images/icons/emoji/unicode/1f4e6.png">📦</g-emoji> Impacted Configuration Items:**\n`;
            result.linkedCis.forEach(ci => {
                body += `- \`${ci.id}\`: ${ci.name} (${ci.type})\n`;
            });
            body += "\n";
        }

        if (result.linkedRisks.length > 0) {
            body += `**<g-emoji class="g-emoji" alias="warning" fallback-src="https://github.githubassets.com/images/icons/emoji/unicode/26a0.png">⚠️</g-emoji> Related Risks:**\n`;
            result.linkedRisks.forEach(r => {
                body += `- \`${r.id}\`: ${r.description}\n`;
            });
             body += "\n";
        }

        body += `> This analysis was auto-generated by Ignition AI.`;
        return body;
    };

    const handlePostComment = async () => {
        if (!result) return;
        setIsPosting(true);
        const body = generateCommentBody();
        await onPostComment(pr.number, body);
        setIsPosting(false);
    }

    if (isLoading) {
        return (
            <div className="flex items-center justify-center p-8">
                <Loader size={24} className="animate-spin text-brand-primary" />
                <p className="ml-4 text-gray-400">AI is analyzing this Pull Request...</p>
            </div>
        );
    }

    if (!result) {
        return (
            <div className="text-center p-8">
                <p className="text-gray-400">Click "Analyze PR" to generate an AI-powered analysis.</p>
            </div>
        );
    }
    
    if (result.error) {
         return (
            <div className="bg-red-900/20 border border-red-700/50 rounded-lg p-4 text-center">
                <div className="flex items-center justify-center gap-2">
                    <AlertTriangle size={20} className="text-red-400" />
                    <p className="font-semibold text-red-300">Analysis Failed</p>
                </div>
                <p className="text-sm text-red-400 mt-2">{result.error}</p>
            </div>
        );
    }

    return (
        <div className="space-y-6">
            <div className="flex justify-between items-start gap-4">
                <h3 className="text-lg font-bold text-brand-primary flex items-center gap-2">
                    <Bot size={20} /> AI Analysis
                </h3>
                <button
                    onClick={handlePostComment}
                    disabled={isPosting}
                    className="flex items-center gap-2 bg-gray-700 text-white font-semibold px-4 py-2 rounded-lg hover:bg-gray-600 transition-colors disabled:opacity-50 disabled:cursor-wait"
                >
                    {isPosting ? <Loader size={16} className="animate-spin" /> : <MessageSquare size={16} />}
                    {isPosting ? 'Posting...' : 'Post to PR'}
                </button>
            </div>
            
            <p className="text-gray-300 bg-gray-800/50 p-4 rounded-lg border border-gray-700">{result.summary}</p>

            <div className="space-y-4">
                <div>
                    <h4 className="font-semibold text-gray-200 mb-2">Linked Requirements</h4>
                    {result.linkedRequirements.length > 0 ? (
                        <div className="flex flex-col gap-2">
                            {result.linkedRequirements.map(item => <LinkedItemTag key={item.id} item={item} />)}
                        </div>
                    ) : <p className="text-sm text-gray-500 italic">No direct requirement links identified.</p>}
                </div>

                <div>
                    <h4 className="font-semibold text-gray-200 mb-2">Impacted Configuration Items</h4>
                    {result.linkedCis.length > 0 ? (
                        <div className="flex flex-col gap-2">
                            {result.linkedCis.map(item => <LinkedItemTag key={item.id} item={item} />)}
                        </div>
                    ) : <p className="text-sm text-gray-500 italic">No direct CI impacts identified.</p>}
                </div>
                
                 <div>
                    <h4 className="font-semibold text-gray-200 mb-2">Related Risks</h4>
                    {result.linkedRisks.length > 0 ? (
                        <div className="flex flex-col gap-2">
                            {result.linkedRisks.map(item => <LinkedItemTag key={item.id} item={item} />)}
                        </div>
                    ) : <p className="text-sm text-gray-500 italic">No related risks identified.</p>}
                </div>
            </div>

             <div>
                <h4 className="font-semibold text-gray-200 mb-2 flex items-center gap-2">
                    <GitCommitVertical size={16}/> Suggested Commit Message
                </h4>
                <div className="bg-gray-800/50 border border-gray-700 rounded-lg p-4 relative font-mono text-sm text-gray-300 whitespace-pre-wrap">
                    {result.suggestedCommitMessage}
                     <button
                        onClick={handleCopy}
                        className="absolute top-2 right-2 p-1.5 rounded-md text-gray-400 bg-gray-700/50 hover:bg-gray-600/50 hover:text-white transition-colors"
                        aria-label="Copy commit message"
                    >
                        {isCopied ? <Check size={16} className="text-green-400" /> : <Copy size={16} />}
                    </button>
                </div>
            </div>

        </div>
    );
};

export default PrAnalysisView;
