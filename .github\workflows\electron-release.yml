name: 🚀 Electron Release - Meta-Compliance Desktop Build

on:
  push:
    tags:
      - 'v*.*.*'
  workflow_dispatch:
    inputs:
      version:
        description: 'Release version (e.g., v1.0.0)'
        required: true
        default: 'v1.0.0'

env:
  GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

jobs:
  build-and-release:
    name: Build & Release Desktop Apps
    runs-on: ${{ matrix.os }}
    
    strategy:
      matrix:
        os: [ubuntu-latest, windows-latest, macos-latest]
        include:
          - os: ubuntu-latest
            platform: linux
            arch: x64
          - os: windows-latest
            platform: win
            arch: x64
          - os: macos-latest
            platform: mac
            arch: universal

    steps:
      - name: 📥 Checkout repository
        uses: actions/checkout@v4

      - name: 🟢 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'npm'

      - name: 📦 Install dependencies
        run: npm ci

      - name: 🧪 Run tests
        run: npm run test:run

      - name: 🏗️ Build web application
        run: npm run build
        env:
          NODE_ENV: production

      - name: 🖥️ Build Electron app (Linux)
        if: matrix.platform == 'linux'
        run: npm run electron:build:linux
        env:
          GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - name: 🖥️ Build Electron app (Windows)
        if: matrix.platform == 'win'
        run: npm run electron:build:win
        env:
          GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - name: 🖥️ Build Electron app (macOS)
        if: matrix.platform == 'mac'
        run: npm run electron:build:mac
        env:
          GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          CSC_IDENTITY_AUTO_DISCOVERY: false

      - name: 📤 Upload artifacts
        uses: actions/upload-artifact@v4
        with:
          name: ignition-${{ matrix.platform }}-${{ matrix.arch }}
          path: |
            dist-electron/*.exe
            dist-electron/*.dmg
            dist-electron/*.AppImage
            dist-electron/*.deb
            dist-electron/*.rpm
          retention-days: 7

  create-release:
    name: 🎉 Create GitHub Release
    needs: build-and-release
    runs-on: ubuntu-latest
    if: startsWith(github.ref, 'refs/tags/') || github.event_name == 'workflow_dispatch'
    
    steps:
      - name: 📥 Checkout repository
        uses: actions/checkout@v4

      - name: 📥 Download all artifacts
        uses: actions/download-artifact@v4
        with:
          path: ./artifacts

      - name: 📋 Generate release notes
        id: release_notes
        run: |
          VERSION="${{ github.event.inputs.version || github.ref_name }}"
          echo "version=${VERSION}" >> $GITHUB_OUTPUT
          
          cat > release_notes.md << 'EOF'
          # 🚀 Ignition Desktop Release ${VERSION}
          
          ## Meta-Compliance Achievement Unlocked! 🏆
          
          This release demonstrates **true meta-compliance** - Ignition has built and deployed itself using the same process management standards it enforces for other projects!
          
          ## 🎯 What's New
          
          - **Desktop Application**: Native desktop app for Windows, macOS, and Linux
          - **File System Persistence**: True local data storage (no localStorage limitations)
          - **Auto-Updates**: Automatic updates via GitHub releases
          - **Enhanced Security**: Sandboxed Electron environment with secure IPC
          - **Menu Integration**: Native OS menu integration for better UX
          - **Wiki Generation**: Built-in documentation generation capabilities
          
          ## 📦 Downloads
          
          Choose your platform:
          
          - **Windows**: `Ignition-Setup-${VERSION}.exe`
          - **macOS**: `Ignition-${VERSION}.dmg` 
          - **Linux**: `Ignition-${VERSION}.AppImage`
          
          ## 🔧 Installation
          
          1. Download the appropriate file for your OS
          2. Install/run the application
          3. Configure your API keys in Settings
          4. Start managing projects with AI-powered compliance!
          
          ## 🎖️ Meta-Compliance Features
          
          - **Self-Documentation**: Ignition documents its own development process
          - **Process Asset Framework**: Reusable templates for requirements, tests, and risks
          - **Compliance Engine**: ISO 27001, SOC 2, HIPAA, FDA support
          - **GitHub Integration**: Seamless repository management and wiki generation
          
          ---
          
          **Built with ❤️ by Castle Bravo Project**
          
          *Demonstrating that the best compliance tools are the ones that comply with themselves!*
          EOF

      - name: 🎉 Create Release
        uses: softprops/action-gh-release@v2
        with:
          tag_name: ${{ steps.release_notes.outputs.version }}
          name: "Ignition Desktop ${{ steps.release_notes.outputs.version }}"
          body_path: release_notes.md
          draft: false
          prerelease: false
          files: |
            artifacts/**/*
          generate_release_notes: true

  notify-success:
    name: 🎊 Success Notification
    needs: [build-and-release, create-release]
    runs-on: ubuntu-latest
    if: success()
    
    steps:
      - name: 🎉 Meta-Compliance Success
        run: |
          echo "🏆 META-COMPLIANCE ACHIEVEMENT UNLOCKED!"
          echo "🚀 Ignition has successfully built and released itself!"
          echo "🎯 The tool now manages its own deployment process!"
          echo "📦 Desktop apps available for Windows, macOS, and Linux!"
          echo "🌟 This is the ultimate demonstration of process compliance!"

  notify-failure:
    name: 🚨 Failure Notification  
    needs: [build-and-release, create-release]
    runs-on: ubuntu-latest
    if: failure()
    
    steps:
      - name: 🚨 Build Failed
        run: |
          echo "❌ Desktop build failed!"
          echo "🔍 Check the logs for details"
          echo "🛠️ Meta-compliance requires fixing this!"
