apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ignition-ingress
  namespace: ignition
  labels:
    app: ignition
    app.kubernetes.io/name: ignition
    app.kubernetes.io/component: ingress
  annotations:
    nginx.ingress.kubernetes.io/rewrite-target: /
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    nginx.ingress.kubernetes.io/proxy-body-size: "16m"
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
spec:
  ingressClassName: nginx
  tls:
  - hosts:
    - ignition.yourdomain.com
    secretName: ignition-tls
  rules:
  - host: ignition.yourdomain.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: ignition-service
            port:
              number: 80
